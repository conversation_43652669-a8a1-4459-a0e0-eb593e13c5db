<template>
  <t-dialog
    width="520px"
    header="设置模板角色"
    :confirmBtn="null"
    :cancelBtn="null"
    :showOverlay="true"
    :preventScrollThrough="true"
    :attach="'body'"
    :closeOnOverlayClick="false"
    :closeOnEscKeydown="false"
    :visible.sync="visible"
    placement="center"
  >
    <div class="dialog-body">
      <t-form :data="formData" :rules="rules" ref="form" @submit="onSubmit" label-align="top">
        <t-form-item label="宏名称" name="macro">
          <t-select v-model="formData.macro" placeholder="请选择一个宏" class="full-width-control">
            <t-option :key="macro.className" :label="macro.roleName" :value="macro.className" v-for="macro in macroList">
              <t-tooltip :content="macro.roleName">
                <div style="width: 100%;height: 100%;pointer-events:auto;">{{macro.roleName}}</div>
              </t-tooltip>
            </t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="模板角色" name="account">
          <t-tooltip :content="formData.account || ''">
            <t-cascader :options="accountList" v-model="formData.account" placeholder="请选择账号和角色" class="full-width-control" @click.native="scheduleCascaderTooltip"/>
          </t-tooltip>
        </t-form-item>
        <div class="footer-actions">
          <t-space size="10px">
            <t-button theme="danger" variant="base" @click="onDelete">删除模板</t-button>
            <t-button theme="primary" @click="onSave">保存为模板</t-button>
          </t-space>
        </div>
      </t-form>
    </div>
  </t-dialog>
</template>

<script>
import { pyObjects } from '@/utils/request'

const defaultFormData = {
  macro: null,
  account: null
}

export default {
  name: 'TemplateDialog',
  props: {
    macroList: {
      type: Array,
      default: () => []
    }
  },
  data(){
    return {
      visible: false,
      accountList: [],
      formData: { ...defaultFormData },
      rules: {
        macro: [{ required: true, message: '请选择宏', type: 'error', trigger: 'blur' }],
        account: [{ required: true, message: '请选择模板角色', type: 'error', trigger: 'blur' }]
      }
    }
  },
  methods:{
    open(currentMacro){
      this.visible = true
      this.formData = { ...defaultFormData }
      // 宏名称默认选中当前主页的宏
      this.formData.macro = currentMacro || null
      this.onGetJxUrl()
      this.getAccountRole()
    },
    onGetJxUrl() {
      pyObjects('tools/getJxUrl').then(res => {
        // 只需触发 jx3 路径初始化，不必写入表单
      })
    },
    getAccountRole() {
      pyObjects('tools/getAccountRole').then(res => {
        if (res.code == 200) {
          this.accountList = res.result
        } else {
          this.accountList = []
        }
      })
    },
    onSave(){
      this.$refs.form.submit()
    },
    onDelete(){
      if(!this.formData.macro){
        this.$message.warning('请选择宏')
        return
      }
      this.$dialog.confirm({
        placement: 'center',
        header: '确认删除',
        body: '删除后需重新设置模板，确认继续？',
        confirmBtn: '删除',
        cancelBtn: '取消',
        onConfirm: () => {
          pyObjects('tools/deleteTemplate', { macroName: this.formData.macro }).then(res => {
            if(res.code == 200){
              this.$message.success('已删除模板')
            }else{
              this.$message.error(res.message || '删除失败')
            }
          })
        }
      })
    },
    onSubmit({ validateResult, firstError }){
      if (validateResult === true) {
        pyObjects('tools/saveSysJx', {
          macroName: this.formData.macro,
          roleUrl: this.formData.account
        }).then(res => {
          if (res.code == 200) {
            this.$message.success('保存成功')
            this.visible = false
          } else {
            this.$message.error(res.message)
          }
        })
      } else {
        this.$message.warning(firstError)
      }
    }
    ,scheduleCascaderTooltip(){
      const ensureTip = () => {
        let tip = document.getElementById('lb-cascade-hover-tip')
        if(!tip){
          tip = document.createElement('div')
          tip.id = 'lb-cascade-hover-tip'
          tip.style.position = 'fixed'
          tip.style.zIndex = '9999'
          tip.style.background = 'rgba(0,0,0,0.85)'
          tip.style.color = '#fff'
          tip.style.padding = '6px 8px'
          tip.style.borderRadius = '4px'
          tip.style.fontSize = '16px'
          tip.style.maxWidth = '60vw'
          tip.style.pointerEvents = 'none'
          tip.style.display = 'none'
          document.body.appendChild(tip)
        }
        return tip
      }
      const apply = () => {
        try{
          const tip = ensureTip()
          const selectors = [
            '.t-cascader__item',
            '.t-cascader__item-label',
            '.t-cascader-item',
            '.t-cascader-item__label',
            '.t-cascader__panel li',
            '.t-cascader .t-input input',
            '.t-cascader .t-input',
            '.t-cascader .t-input__inner',
          ]
          const nodes = document.querySelectorAll(selectors.join(','))
          nodes.forEach(el=>{ try{ el.removeAttribute('title') }catch(e){} })
          nodes
            .forEach(el=>{
              const txt = (el.innerText || el.textContent || '').trim()
              if (!txt) return
              if (!el.dataset.lbTipBound){
                el.dataset.lbTipBound = '1'
                el.addEventListener('mouseenter', (e)=>{
                  tip.textContent = txt
                  tip.style.display = 'block'
                })
                el.addEventListener('mousemove', (e)=>{
                  const x = e.clientX + 12
                  const y = e.clientY + 12
                  tip.style.left = x + 'px'
                  tip.style.top = y + 'px'
                })
                el.addEventListener('mouseleave', ()=>{
                  tip.style.display = 'none'
                })
              }
            })
        }catch(e){}
      }
      this.$nextTick(()=>{
        apply()
        setTimeout(apply, 150)
        setTimeout(apply, 400)
      })
    }
  }
}
</script>

<style scoped>
.dialog-body { padding: 10px 5px; }
.full-width-control { width: 100%; }
</style>


