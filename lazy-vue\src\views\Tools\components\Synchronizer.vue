<template>
  <t-dialog
    width="760px"
    :closeOnEscKeydown="false"
    :closeOnOverlayClick="false"
    header="直接同步"
    :preventScrollThrough="false"
    :confirmBtn="null"
    :cancelBtn="null"
    placement="center" :visible.sync="visible">
    <div class="dialog-body">
      <t-form :data="formData" :rules="rules" ref="form" @submit="onSubmit" label-align="top">
        <t-form-item label="剑三游戏路径" help="请选择剑三安装目录下的 bin/zhcn_hd 文件夹" name="jx3Url">
          <div class="input-with-button full-width-control">
            <t-input :readonly="true" v-model="formData.jx3Url" placeholder="请选择路径"></t-input>
            <div btn class="t-button t-button--variant-outline" @click="onSetJxUrl">选择</div>
          </div>
        </t-form-item>

        <div class="section">
          <div class="row">
            <t-form-item label="源角色" name="sourceAccount" help="作为同步来源的角色">
              <t-tooltip :content="formData.sourceAccount || ''">
                <t-cascader :options="accountList" v-model="formData.sourceAccount" placeholder="请选择源账号和角色" class="half-width-control" @click.native="scheduleCascaderTooltip" @mousedown.native="scheduleCascaderTooltip"/>
              </t-tooltip>
            </t-form-item>
            <t-form-item label="目标角色" name="targetAccount" help="被同步到的角色" style="margin-left: 50px;">
              <t-tooltip :content="formData.targetAccount || ''">
                <t-cascader :options="accountList" v-model="formData.targetAccount" placeholder="请选择目标账号和角色" class="half-width-control" @click.native="scheduleCascaderTooltip" @mousedown.native="scheduleCascaderTooltip"/>
              </t-tooltip>
            </t-form-item>
          </div>
          <t-form-item>
            <t-space size="10px">
              <t-button theme="primary" @click="onDirectSyncUI" style="margin-top: 10px;">同步键位</t-button>
              <t-button variant="base" @click="onDirectSyncPlug" style="margin-top: 10px;">同步插件</t-button>
            </t-space>
          </t-form-item>
        </div>
      </t-form>
    </div>
  </t-dialog>
</template>

<script>
import { pyObjects } from '@/utils/request'

let defaultFormData = {
  jx3Url: null,
  macro: null,
  account: null,
  sourceAccount: null,
  targetAccount: null
}
export default {
  name: 'Synchronizer',
  data() {
    return {
      visible: false,
      accountList: [],
      formData: { ...defaultFormData },
      macroList: [],
      rules: {
        jx3Url: [
          {
            required: true,
            message: '请选择路径',
            type: 'error',
            trigger: 'blur',
          }
        ],
        macro: [
          {
            required: true,
            message: '请选择绑定的宏',
            type: 'error',
            trigger: 'blur',
          }
        ],
        account: [
          {
            required: true,
            message: '请选择模板角色',
            type: 'error',
            trigger: 'blur',
          }
        ]
      }
    }
  },
  methods: {
    open() {
      this.visible = true
      this.formData = { ...defaultFormData }

      this.onGetJxUrl()
      this.getAccountRole()
      this.getMacro()
    },
    scheduleCascaderTooltip(){
      const ensureTip = () => {
        let tip = document.getElementById('lb-cascade-hover-tip')
        if(!tip){
          tip = document.createElement('div')
          tip.id = 'lb-cascade-hover-tip'
          tip.style.position = 'fixed'
          tip.style.zIndex = '9999'
          tip.style.background = 'rgba(0,0,0,0.85)'
          tip.style.color = '#fff'
          tip.style.padding = '6px 8px'
          tip.style.borderRadius = '4px'
          tip.style.fontSize = '16px'
          tip.style.maxWidth = '60vw'
          tip.style.pointerEvents = 'none'
          tip.style.display = 'none'
          document.body.appendChild(tip)
        }
        return tip
      }
      const bindDelegated = () => {
        if (window.__lbCascadeTipBound) return
        window.__lbCascadeTipBound = true
        const tip = ensureTip()
        const isCascadeEl = (target) => !!(target && target.closest && target.closest('.t-cascader__panel'))
        const getText = (el) => (el.innerText || el.textContent || '').trim()
        document.addEventListener('mouseover', (e)=>{
          const el = e.target.closest && e.target.closest('.t-cascader__item, .t-cascader-item, .t-cascader__item-label, .t-cascader-item__label, .t-cascader__panel li')
          if (!el || !isCascadeEl(el)) return
          const txt = getText(el)
          if (!txt) return
          el.removeAttribute && el.removeAttribute('title')
          tip.textContent = txt
          tip.style.display = 'block'
        }, true)
        document.addEventListener('mousemove', (e)=>{
          if (tip.style.display !== 'block') return
          tip.style.left = (e.clientX + 12) + 'px'
          tip.style.top = (e.clientY + 12) + 'px'
        }, true)
        document.addEventListener('mouseout', (e)=>{
          const to = e.relatedTarget
          if (!to || !(to.closest && to.closest('.t-cascader__panel'))) {
            tip.style.display = 'none'
          }
        }, true)
      }
      this.$nextTick(()=>{
        // 初始移除可能的 title
        try{
          document.querySelectorAll('.t-cascader [title]').forEach(el=>el.removeAttribute('title'))
        }catch(e){}
        bindDelegated()
      })
    },
    scheduleCascaderTooltip(){},
    onSaveTemplate(){
      this.$refs.form.submit()
    },
    onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        pyObjects("tools/saveSysJx",{
          macroName: this.formData.macro,
          roleUrl: this.formData.account
        }).then(res=>{
        if(res.code == 200){
          this.$message.success('保存成功')
          // 不关闭弹窗，便于继续操作
        }else{
          this.$message.error(res.message)
        }
      })
      } else {
        console.log('Errors: ', validateResult)
        this.$message.warning(firstError)
      }
    },
    onDirectSyncUI(){
      if(!this.formData.sourceAccount || !this.formData.targetAccount){
        this.$message.warning('请选择源角色和目标角色')
        return
      }
      pyObjects("tools/synUIByRole",{
        sourceRoleUrl: this.formData.sourceAccount,
        targetRoleUrl: this.formData.targetAccount
      }).then(res=>{
        if(res.code == 200){
          this.$message.success('同步键位成功')
        }else{
          this.$message.error(res.message)
        }
      })
    },
    onDirectSyncPlug(){
      if(!this.formData.sourceAccount || !this.formData.targetAccount){
        this.$message.warning('请选择源角色和目标角色')
        return
      }
      pyObjects("tools/synPlugByRole",{
        sourceRoleUrl: this.formData.sourceAccount,
        targetRoleUrl: this.formData.targetAccount
      }).then(res=>{
        if(res.code == 200){
          this.$message.success('同步插件成功')
        }else{
          this.$message.error(res.message)
        }
      })
    },
    onGetJxUrl() {
      pyObjects("tools/getJxUrl").then(res=>{
        if(res.code == 200){
          this.formData.jx3Url = res.result
        }
      })
    },
    onSetJxUrl() {
      pyObjects("tools/setJxUrl").then(res=>{
        if(res.code == 200){
          this.formData.jx3Url = res.result
          this.getAccountRole()
        }
      })
    },
    getAccountRole() {
      pyObjects("tools/getAccountRole").then(res=>{
        if(res.code == 200){
          this.accountList = res.result
        }else{
          this.accountList = []
        }
      })
    },
    getMacro(){
      pyObjects("macro/initMacro",null).then(res=>{
        if(res.code == 200){
          this.macroList = res.result.macroList
        }
      })
    }
  },
}
</script>

<style scoped>
.dialog-body {
  padding: 10px 5px;
}
.section{margin-top:8px}
.row{display:flex;gap:16px;align-items:flex-start;flex-wrap:wrap}
.col-title{font-weight:600;margin:4px 0 8px;color:#555}
.input-with-button {
  display: flex;
  align-items: center;
}
.input-with-button .t-input {
  flex-grow: 1;
}
.input-with-button .t-button {
  margin-left: 12px;
  flex-shrink: 0;
  height: 32px;
  padding: 0 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-weight: 500;
  cursor: pointer;
}

.full-width-control {
  width: 100%;
}
.half-width-control { width: 280px; }
</style>