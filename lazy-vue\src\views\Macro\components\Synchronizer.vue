<template>
  <t-dialog
    :closeOnEscKeydown="false"
    :closeOnOverlayClick="false"
    :showOverlay="true"
    :preventScrollThrough="true"
    :attach="'body'"
    :header="'快速同步'"
    :confirmBtn="null"
    :cancelBtn="null"
    placement="center" :visible.sync="visible">
    <div style="display: flex;justify-content: center;align-items: center;flex-direction: column;">
      <t-form :data="formData" :rules="rules" ref="form" @submit="onSubmit" style="width: 100%;">
        <t-form-item label="模板" help="请选择已设置的模板（宏对应的模板角色）" name="template">
          <t-select v-model="formData.template" placeholder="请选择模板">
            <t-option :key="tpl.macroName" :label="macroLabel(tpl.macroName)" :value="tpl.macroName" v-for="tpl in templateList">
              <t-tooltip :content="macroLabel(tpl.macroName)">
                <div style="width: 100%;height: 100%;pointer-events:auto;">{{ macroLabel(tpl.macroName) }}</div>
              </t-tooltip>
            </t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="角色" help="请选择需要同步的角色" name="account">
          <t-tooltip :content="formData.account || ''">
            <t-cascader :options="accountList" v-model="formData.account" @click.native="scheduleCascaderTooltip"/>
          </t-tooltip>
        </t-form-item>
        <t-form-item style="margin-left: 100px">
          <t-space size="10px">
            <t-button theme="primary" @click="onSynUI">同步键位</t-button>
            <t-button theme="default" variant="base" @click="onSynPlug">同步插件</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
</template>

<script>
import { pyObjects } from '@/utils/request'

let defaultFormData = {
  account: null,
  template: null
}
export default {
  name: 'Synchronizer',
  data() {
    return {
      visible: false,
      accountList: [],
      formData: { ...defaultFormData },
      templateList: [],
      type:0,
      rules: {
        account: [
          {
            required: true,
            message: '请选择需要同步角色',
            type: 'error',
            trigger: 'blur',
          }
        ]
      }
    }
  },
  props: {
    role:{
      type:String,
      default:''
    },
    macroList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    open() {
      this.visible = true
      this.formData = { ...defaultFormData }

      this.getAccountRole()
      // 拉取模板后校验当前宏是否已有模板
      this.getTemplates().then(() => {
        if (this.role) {
          const exists = this.templateList.some(t => t.macroName === this.role)
          if (!exists) {
            this.$message.error('当前未设置模板，请先设置模板')
            this.visible = false
            return
          }
          // 默认选中主页当前宏对应的模板名
          this.formData.template = this.role
        }
      })
    },
    onConfirm() {
      this.visible = false
    },
    onSynUI(){
      this.type = 0
      this.$refs.form.submit()
    },
    onSynPlug(){
      this.type = 1
      this.$refs.form.submit()
    },
    onSubmit({ validateResult, firstError }) {
      if (validateResult === true) {
        // 校验是否存在模板
        if(!this.formData.template || !this.templateList.some(t => t.macroName === this.formData.template)){
          this.$message.error('当前未设置模板，请先设置模板')
          return
        }
        let uri = ''
        if(this.type == 0) {
          uri = "tools/synUI"
        }else if (this.type == 1){
          uri = "tools/synPlug"
        }
        pyObjects(uri,{
          roleUrl: this.formData.account,
          macroName: this.formData.template
        }).then(res=>{
          if(res.code == 200){
            this.$message.success('同步成功')
            // this.visible = false
          }else{
            this.$message.error(res.message)
          }
        })
      } else {
        console.log('Errors: ', validateResult)
        this.$message.warning(firstError)
      }
    },
    getAccountRole() {
      pyObjects("tools/getAccountRole").then(res=>{
        if(res.code == 200){
          this.accountList = res.result
        }else{
          this.accountList = []
        }
      })
    },
    getTemplates(){
      return pyObjects('tools/getTemplates').then(res => {
        if(res.code == 200){
          this.templateList = res.result || []
        }else{
          this.templateList = []
        }
        return this.templateList
      })
    },
    macroLabel(className){
      // 在主页传入的 macroList 中找中文名
      const m = Array.isArray(this.macroList) ? this.macroList.find(i => i.className === className) : null
      return m ? m.roleName : className
    },
    scheduleCascaderTooltip(){
      const ensureTip = () => {
        let tip = document.getElementById('lb-cascade-hover-tip')
        if(!tip){
          tip = document.createElement('div')
          tip.id = 'lb-cascade-hover-tip'
          tip.style.position = 'fixed'
          tip.style.zIndex = '9999'
          tip.style.background = 'rgba(0,0,0,0.85)'
          tip.style.color = '#fff'
          tip.style.padding = '6px 8px'
          tip.style.borderRadius = '4px'
          tip.style.fontSize = '16px'
          tip.style.maxWidth = '60vw'
          tip.style.pointerEvents = 'none'
          tip.style.display = 'none'
          document.body.appendChild(tip)
        }
        return tip
      }
      const apply = () => {
        try{
          const tip = ensureTip()
          const selectors = [
            '.t-cascader__item',
            '.t-cascader__item-label',
            '.t-cascader-item',
            '.t-cascader-item__label',
            '.t-cascader__panel li',
            '.t-cascader .t-input input',
            '.t-cascader .t-input',
            '.t-cascader .t-input__inner',
          ]
          const nodes = document.querySelectorAll(selectors.join(','))
          nodes.forEach(el=>{ try{ el.removeAttribute('title') }catch(e){} })
          nodes
            .forEach(el=>{
              const txt = (el.innerText || el.textContent || '').trim()
              if (!txt) return
              if (!el.dataset.lbTipBound){
                el.dataset.lbTipBound = '1'
                el.addEventListener('mouseenter', (e)=>{
                  tip.textContent = txt
                  tip.style.display = 'block'
                })
                el.addEventListener('mousemove', (e)=>{
                  const x = e.clientX + 12
                  const y = e.clientY + 12
                  tip.style.left = x + 'px'
                  tip.style.top = y + 'px'
                })
                el.addEventListener('mouseleave', ()=>{
                  tip.style.display = 'none'
                })
              }
            })
        }catch(e){}
      }
      this.$nextTick(()=>{
        apply()
        setTimeout(apply, 150)
        setTimeout(apply, 400)
      })
    }
  },
}
</script>

<style>
  .user-info-card{
      margin: 20px;
      /* width: 40%; */
  }
  .code{
    width: 200px;
    height: 200px;
  }
</style>